package benchmark

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/prometheus/client_golang/prometheus/push"
)

type BenchmarkMetrics struct {
	registry *prometheus.Registry
	pusher   *push.Pusher

	operationsPerSecond *prometheus.GaugeVec
	memoryAllocations   *prometheus.GaugeVec
	latencyHistogram    *prometheus.HistogramVec

	dbQueryDuration     *prometheus.HistogramVec
	dbConnectionsActive *prometheus.GaugeVec

	grpcRequestDuration *prometheus.HistogramVec
	grpcRequestsTotal   *prometheus.CounterVec

	setupDuration   *prometheus.HistogramVec
	cleanupDuration *prometheus.HistogramVec
	testDuration    *prometheus.HistogramVec

	concurrentUsers *prometheus.GaugeVec
	errorRate       *prometheus.GaugeVec

	serviceName string
	pushGateway string
	mu          sync.RWMutex
}

func NewBenchmarkMetrics(serviceName, pushGateway string) *BenchmarkMetrics {
	registry := prometheus.NewRegistry()

	bm := &BenchmarkMetrics{
		registry:    registry,
		serviceName: serviceName,
		pushGateway: pushGateway,
	}

	bm.initializeMetrics()

	if pushGateway != "" {
		bm.pusher = push.New(pushGateway, "benchmark_job").
			Gatherer(registry).
			Grouping("service", serviceName)
	}

	return bm
}

func (bm *BenchmarkMetrics) initializeMetrics() {
	bm.operationsPerSecond = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "benchmark_operations_per_second",
			Help: "Number of operations per second in benchmark tests",
		},
		[]string{"service", "benchmark_name", "operation_type"},
	)

	bm.memoryAllocations = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "benchmark_memory_allocations_per_op",
			Help: "Memory allocations per operation in benchmark tests",
		},
		[]string{"service", "benchmark_name", "operation_type"},
	)

	bm.latencyHistogram = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "benchmark_latency_seconds",
			Help:    "Latency distribution for benchmark operations",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"service", "benchmark_name", "operation_type"},
	)

	bm.dbQueryDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "benchmark_db_query_duration_seconds",
			Help:    "Database query duration in benchmark tests",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0},
		},
		[]string{"service", "benchmark_name", "query_type", "table"},
	)

	bm.dbConnectionsActive = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "benchmark_db_connections_active",
			Help: "Number of active database connections during benchmark",
		},
		[]string{"service", "benchmark_name"},
	)

	bm.grpcRequestDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "benchmark_grpc_request_duration_seconds",
			Help:    "gRPC request duration in benchmark tests",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0},
		},
		[]string{"service", "benchmark_name", "method", "status"},
	)

	bm.grpcRequestsTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "benchmark_grpc_requests_total",
			Help: "Total number of gRPC requests in benchmark tests",
		},
		[]string{"service", "benchmark_name", "method", "status"},
	)

	bm.setupDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "benchmark_setup_duration_seconds",
			Help:    "Time taken to setup benchmark environment",
			Buckets: []float64{0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0},
		},
		[]string{"service", "benchmark_name"},
	)

	bm.cleanupDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "benchmark_cleanup_duration_seconds",
			Help:    "Time taken to cleanup benchmark environment",
			Buckets: []float64{0.1, 0.5, 1.0, 2.0, 5.0, 10.0},
		},
		[]string{"service", "benchmark_name"},
	)

	bm.testDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "benchmark_test_duration_seconds",
			Help:    "Total duration of benchmark test execution",
			Buckets: []float64{1.0, 5.0, 10.0, 30.0, 60.0, 120.0, 300.0},
		},
		[]string{"service", "benchmark_name"},
	)

	bm.concurrentUsers = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "benchmark_concurrent_users",
			Help: "Number of concurrent users in load test",
		},
		[]string{"service", "benchmark_name"},
	)

	bm.errorRate = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "benchmark_error_rate",
			Help: "Error rate percentage in benchmark tests",
		},
		[]string{"service", "benchmark_name", "error_type"},
	)

	bm.registry.MustRegister(
		bm.operationsPerSecond,
		bm.memoryAllocations,
		bm.latencyHistogram,
		bm.dbQueryDuration,
		bm.dbConnectionsActive,
		bm.grpcRequestDuration,
		bm.grpcRequestsTotal,
		bm.setupDuration,
		bm.cleanupDuration,
		bm.testDuration,
		bm.concurrentUsers,
		bm.errorRate,
	)
}

func (bm *BenchmarkMetrics) RecordOperationsPerSecond(benchmarkName, operationType string, opsPerSec float64) {
	bm.operationsPerSecond.WithLabelValues(bm.serviceName, benchmarkName, operationType).Set(opsPerSec)
}

func (bm *BenchmarkMetrics) RecordMemoryAllocations(benchmarkName, operationType string, allocsPerOp float64) {
	bm.memoryAllocations.WithLabelValues(bm.serviceName, benchmarkName, operationType).Set(allocsPerOp)
}

func (bm *BenchmarkMetrics) RecordLatency(benchmarkName, operationType string, duration time.Duration) {
	bm.latencyHistogram.WithLabelValues(bm.serviceName, benchmarkName, operationType).Observe(duration.Seconds())
}

func (bm *BenchmarkMetrics) RecordDatabaseQuery(benchmarkName, queryType, table string, duration time.Duration) {
	bm.dbQueryDuration.WithLabelValues(bm.serviceName, benchmarkName, queryType, table).Observe(duration.Seconds())
}

func (bm *BenchmarkMetrics) RecordDatabaseConnections(benchmarkName string, activeConnections int) {
	bm.dbConnectionsActive.WithLabelValues(bm.serviceName, benchmarkName).Set(float64(activeConnections))
}

func (bm *BenchmarkMetrics) RecordGRPCRequest(benchmarkName, method, status string, duration time.Duration) {
	bm.grpcRequestDuration.WithLabelValues(bm.serviceName, benchmarkName, method, status).Observe(duration.Seconds())
	bm.grpcRequestsTotal.WithLabelValues(bm.serviceName, benchmarkName, method, status).Inc()
}

func (bm *BenchmarkMetrics) RecordSetupDuration(benchmarkName string, duration time.Duration) {
	bm.setupDuration.WithLabelValues(bm.serviceName, benchmarkName).Observe(duration.Seconds())
}

func (bm *BenchmarkMetrics) RecordCleanupDuration(benchmarkName string, duration time.Duration) {
	bm.cleanupDuration.WithLabelValues(bm.serviceName, benchmarkName).Observe(duration.Seconds())
}

func (bm *BenchmarkMetrics) RecordTestDuration(benchmarkName string, duration time.Duration) {
	bm.testDuration.WithLabelValues(bm.serviceName, benchmarkName).Observe(duration.Seconds())
}

func (bm *BenchmarkMetrics) RecordConcurrentUsers(benchmarkName string, users int) {
	bm.concurrentUsers.WithLabelValues(bm.serviceName, benchmarkName).Set(float64(users))
}

func (bm *BenchmarkMetrics) RecordErrorRate(benchmarkName, errorType string, rate float64) {
	bm.errorRate.WithLabelValues(bm.serviceName, benchmarkName, errorType).Set(rate)
}

func (bm *BenchmarkMetrics) PushMetrics() error {
	if bm.pusher == nil {
		return fmt.Errorf("push gateway not configured")
	}

	return bm.pusher.Push()
}

func (bm *BenchmarkMetrics) StartMetricsServer(port int) *http.Server {
	mux := http.NewServeMux()
	mux.Handle("/metrics", promhttp.HandlerFor(bm.registry, promhttp.HandlerOpts{}))

	server := &http.Server{
		Addr:    ":" + strconv.Itoa(port),
		Handler: mux,
	}

	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			fmt.Printf("Metrics server error: %v\n", err)
		}
	}()

	return server
}

func (bm *BenchmarkMetrics) Shutdown(ctx context.Context, server *http.Server) error {
	if bm.pusher != nil {
		if err := bm.PushMetrics(); err != nil {
			fmt.Printf("Failed to push final metrics: %v\n", err)
		}
	}

	if server != nil {
		return server.Shutdown(ctx)
	}

	return nil
}
