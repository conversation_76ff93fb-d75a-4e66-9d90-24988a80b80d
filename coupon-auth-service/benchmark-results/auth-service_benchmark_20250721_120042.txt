Loaded environment file: ../.env.test
✓ Benchmark metrics enabled - Push Gateway: http://localhost:9091, Metrics Port: 9092
{"level":"error","msg":"failed to initialize database, got error failed to connect to `user=lap60627 database=auth_db`: hostname resolving error: lookup user=auth_service: no such host","time":"2025-07-21T12:00:47.079+07:00"}
Failed to setup auth benchmark environment: failed to connect to database: failed to connect to database: failed to connect to `user=lap60627 database=auth_db`: hostname resolving error: lookup user=auth_service: no such host
exit status 1
FAIL	gitlab.zalopay.vn/phunn4/coupon-auth-service/benchmark	0.601s
FAIL
