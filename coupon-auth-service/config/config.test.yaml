service:
  name: "auth-service-benchmark"

database:
  host: "${BENCHMARK_DB_HOST:-0.0.0.0}"
  port: ${BENCHMARK_DB_PORT:-5432}
  user: "${POSTGRES_USER}"
  password: "${POSTGRES_PASSWORD}"
  name: "${POSTGRES_DB}"
  ssl_mode: "disable"
  max_open_conns: 20
  max_idle_conns: 10
  max_lifetime: "1h"

auth:
  bootstrap_token: "${BOOTSTRAP_TOKEN}"

logging:
  level: "info"
  format: "json"

grpc:
  host: "0.0.0.0"
  port: 50051
  max_receive_size: 4194304
  max_send_size: 4194304
  connection_timeout: "5s"
  keepalive_time: "30s"
  keepalive_timeout: "5s"
  max_connection_idle: "90s"

benchmark_metrics:
  enabled: true
  push_gateway: "http://localhost:9091"
  metrics_port: 9092
  push_interval_seconds: 15
  retention_days: 30
  export_to_file: true
  output_directory: "./benchmark-results"
