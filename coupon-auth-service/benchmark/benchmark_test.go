package benchmark

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"

	proto_auth_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/benchmark"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

var (
	grpcSuite        *benchmark.GRPCBenchmarkSuite
	dbSuite          *benchmark.DatabaseBenchmarkSuite
	testDB           *database.DB
	generator        *benchmark.TestDataGenerator
	benchmarkWrapper *benchmark.BenchmarkWrapper
	cfg              *config.Config
)

func TestMain(m *testing.M) {
	if err := setupAuthBenchmarkEnvironment(); err != nil {
		fmt.Printf("Failed to setup auth benchmark environment: %v\n", err)
		os.Exit(1)
	}

	code := m.Run()
	cleanupAuth()
	os.Exit(code)
}

func setupAuthBenchmarkEnvironment() error {
	cfg, err := config.LoadBenchmarkConfig()
	if err != nil {
		return fmt.Errorf("failed to load benchmark config: %w", err)
	}

	if cfg.BenchmarkMetrics.Enabled {
		benchmarkWrapper = benchmark.NewBenchmarkWrapper(
			cfg.Service.Name,
			cfg.BenchmarkMetrics.PushGateway,
			cfg.BenchmarkMetrics.MetricsPort,
		)
		fmt.Printf("✓ Benchmark metrics enabled - Push Gateway: %s, Metrics Port: %d\n",
			cfg.BenchmarkMetrics.PushGateway, cfg.BenchmarkMetrics.MetricsPort)
	}

	logger := logging.New("info", "json")
	appMetrics := metrics.New(cfg.Service.Name)

	testDB, err = database.NewPostgresDB(&cfg.Database, logger, appMetrics)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	if err := setupBenchmarkTables(); err != nil {
		return fmt.Errorf("failed to setup benchmark tables: %w", err)
	}

	grpcConfig := &benchmark.GRPCBenchmarkConfig{
		ServiceAddress: "0.0.0.0:50051",
		ServiceName:    cfg.Service.Name,
		Timeout:        5 * time.Second,
	}

	grpcSuite, err = benchmark.NewGRPCBenchmarkSuite(grpcConfig)
	if err != nil {
		return fmt.Errorf("failed to create gRPC benchmark suite: %w", err)
	}

	dbConfig := &benchmark.DatabaseBenchmarkConfig{
		ServiceName:    cfg.Service.Name,
		DatabaseType:   "postgres",
		ConnectionPool: 20,
		QueryTimeout:   5 * time.Second,
	}
	dbSuite = benchmark.NewDatabaseBenchmarkSuite(dbConfig, testDB.DB)

	generator = benchmark.NewTestDataGenerator()

	if err := setupBenchmarkTestData(); err != nil {
		return fmt.Errorf("failed to setup benchmark test data: %w", err)
	}

	return nil
}

func setupBenchmarkTables() error {
	models := []any{
		&BenchmarkServiceCredential{},
	}

	if err := testDB.AutoMigrate(models...); err != nil {
		return fmt.Errorf("failed to auto-migrate benchmark tables: %w", err)
	}

	return nil
}

func setupBenchmarkTestData() error {
	testCredential := &BenchmarkServiceCredential{
		Name:      "test-service",
		ClientID:  "test-client-id",
		ClientKey: "$2a$10$N9qo8uLOickgx2ZMRZoMye.IjPeGvGzjYwSxp6ey7TL5bLW8B2FyC",
		Version:   "1.0.0",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	var existingCredential BenchmarkServiceCredential
	result := testDB.Where("client_id = ?", testCredential.ClientID).First(&existingCredential)
	if result.Error != nil {
		if err := testDB.Create(testCredential).Error; err != nil {
			return fmt.Errorf("failed to create test service credential: %w", err)
		}
	}

	return nil
}

func cleanupAuth() {
	if benchmarkWrapper != nil {
		benchmarkWrapper.RecordCleanupMetrics("auth_service_cleanup", func() error {
			if testDB != nil {
				result := testDB.Exec("DELETE FROM service_credentials_benchmark_test")
				if result.Error != nil {
					return result.Error
				}
				fmt.Printf("✓ Cleaned up %d benchmark test records\n", result.RowsAffected)
			}
			return nil
		})

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		benchmarkWrapper.Shutdown(ctx)
	}

	if grpcSuite != nil {
		grpcSuite.Close()
	}
	if testDB != nil {
		sqlDB, _ := testDB.DB.DB()
		sqlDB.Close()
	}
}

func BenchmarkAuthService_gRPC_ValidateServiceCredentials(b *testing.B) {
	if benchmarkWrapper != nil {
		benchmarkWrapper.RunGRPCBenchmarkWithMetrics(b, "validate_service_credentials", "ValidateServiceCredentials", func(b *testing.B) error {
			conn, err := grpc.NewClient("localhost:50051", grpc.WithTransportCredentials(insecure.NewCredentials()))
			if err != nil {
				return err
			}
			defer conn.Close()

			client := proto_auth_v1.NewAuthServiceClient(conn)
			req := &proto_auth_v1.ValidateServiceCredentialsRequest{
				ClientId:    "test-client-id",
				ClientKey:   "test-client-key",
				ServiceName: "test-service",
			}

			_, err = client.ValidateServiceCredentials(context.Background(), req)
			return err
		})
	} else {
		grpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
			client := proto_auth_v1.NewAuthServiceClient(conn)

			req := &proto_auth_v1.ValidateServiceCredentialsRequest{
				ClientId:    "test-client-id",
				ClientKey:   "test-client-key",
				ServiceName: "test-service",
			}
			_, err := client.ValidateServiceCredentials(ctx, req)
			return err
		})
	}
}

// func BenchmarkAuthService_gRPC_RegisterService(b *testing.B) {
// 	grpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
// 		client := proto_auth_v1.NewAuthServiceClient(conn)
// 		ctx = addAuthAuthMetadata(ctx)
//
// 		req := &proto_auth_v1.RegisterServiceRequest{
// 			ServiceName:    fmt.Sprintf("test-service-%d", generator.GenerateUserID()),
// 			ServiceVersion: "1.0.0",
// 			Description:    "Test service for benchmarking",
// 		}
//
// 		_, err := client.RegisterService(ctx, req)
// 		return err
// 	})
// }
//
// func BenchmarkAuthService_gRPC_HealthCheck(b *testing.B) {
// 	grpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
// 		client := proto_auth_v1.NewAuthServiceClient(conn)
// 		ctx = addAuthAuthMetadata(ctx)
//
// 		_, err := client.ValidateServiceCredentials(ctx, &proto_auth_v1.ValidateServiceCredentialsRequest{
// 			ClientId:    "health-check-client",
// 			ClientKey:   "health-check-key",
// 			ServiceName: "health-check",
// 		})
// 		return err
// 	})
// }
//
// func BenchmarkAuthService_Database_ValidateServiceCredentials(b *testing.B) {
// 	dbSuite.BenchmarkQuery(b, "validate_service_credentials", func(ctx context.Context, db *gorm.DB) error {
// 		var credential struct {
// 			ID        int    `gorm:"column:id"`
// 			ClientID  string `gorm:"column:client_id"`
// 			ClientKey string `gorm:"column:client_key"`
// 			IsActive  bool   `gorm:"column:is_active"`
// 		}
//
// 		return db.Table("service_credentials").
// 			Where("client_id = ? AND client_key = ? AND is_active = true",
// 				"test-client-id", "test-client-key").
// 			First(&credential).Error
// 	})
// }
//
// func BenchmarkAuthService_Database_CreateServiceCredential(b *testing.B) {
// 	dbSuite.BenchmarkQuery(b, "create_service_credential", func(ctx context.Context, db *gorm.DB) error {
// 		credential := map[string]any{
// 			"client_id":    fmt.Sprintf("client_%d", generator.GenerateUserID()),
// 			"client_key":   fmt.Sprintf("key_%d", generator.GenerateUserID()),
// 			"service_name": "test-service",
// 			"is_active":    true,
// 			"created_at":   time.Now(),
// 			"updated_at":   time.Now(),
// 		}
// 		return db.Table("service_credentials").Create(credential).Error
// 	})
// }
//
// func BenchmarkAuthService_Database_TokenOperations(b *testing.B) {
// 	dbSuite.BenchmarkTransaction(b, "token_operations", func(ctx context.Context, tx *gorm.DB) error {
// 		token := map[string]any{
// 			"user_id":      generator.GenerateUserID(),
// 			"token_hash":   fmt.Sprintf("hash_%d", generator.GenerateUserID()),
// 			"refresh_hash": fmt.Sprintf("refresh_%d", generator.GenerateUserID()),
// 			"expires_at":   time.Now().Add(24 * time.Hour),
// 			"is_active":    true,
// 			"created_at":   time.Now(),
// 		}
//
// 		if err := tx.Table("user_tokens").Create(token).Error; err != nil {
// 			return err
// 		}
//
// 		return tx.Table("user_tokens").
// 			Where("id = ?", token["id"]).
// 			Update("last_used_at", time.Now()).Error
// 	})
// }
//
// func BenchmarkAuthService_Database_RevokeToken(b *testing.B) {
// 	dbSuite.BenchmarkQuery(b, "revoke_token", func(ctx context.Context, db *gorm.DB) error {
// 		return db.Table("user_tokens").
// 			Where("token_hash = ?", fmt.Sprintf("hash_%d", generator.GenerateUserID())).
// 			Update("is_active", false).Error
// 	})
// }
//
// func BenchmarkAuthService_JWTGeneration(b *testing.B) {
// 	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
// 		ServiceName:    "auth-service",
// 		TestName:       "jwt_generation_performance",
// 		MetricsEnabled: true,
// 	})
//
// 	operation := func(ctx context.Context) error {
// 		ctx = addAuthAuthMetadata(ctx)
//
// 		conn, err := grpc.NewClient("localhost:50051", grpc.WithTransportCredentials(insecure.NewCredentials()))
// 		if err != nil {
// 			return err
// 		}
// 		defer conn.Close()
//
// 		client := proto_auth_v1.NewAuthServiceClient(conn)
// 		req := &proto_auth_v1.ValidateServiceCredentialsRequest{
// 			ClientId:    "benchmark-client",
// 			ClientKey:   "benchmark-key",
// 			ServiceName: "benchmark-service",
// 		}
//
// 		_, err = client.ValidateServiceCredentials(ctx, req)
// 		return err
// 	}
//
// 	framework.RunBenchmark(b, operation)
// }
//
// func BenchmarkAuthService_ConcurrentLoad(b *testing.B) {
// 	scenario := &benchmark.LoadTestScenario{
// 		Name:        "auth-service-mixed-load",
// 		Duration:    30 * time.Second,
// 		Concurrency: 20,
// 		RampUpTime:  5 * time.Second,
// 		Operations: []benchmark.LoadTestOperation{
// 			{
// 				Name:   "validate_service_credentials",
// 				Weight: 50,
// 				Execute: func(ctx context.Context) error {
// 					conn, err := grpc.NewClient("localhost:50051", grpc.WithTransportCredentials(insecure.NewCredentials()))
// 					if err != nil {
// 						return err
// 					}
// 					defer conn.Close()
//
// 					client := proto_auth_v1.NewAuthServiceClient(conn)
// 					req := &proto_auth_v1.ValidateServiceCredentialsRequest{
// 						ClientId:    "test-client-id",
// 						ClientKey:   "test-client-key",
// 						ServiceName: "test-service",
// 					}
// 					_, err = client.ValidateServiceCredentials(ctx, req)
// 					return err
// 				},
// 			},
// 			{
// 				Name:   "register_service",
// 				Weight: 50,
// 				Execute: func(ctx context.Context) error {
// 					ctx = addAuthAuthMetadata(ctx)
//
// 					conn, err := grpc.NewClient("localhost:50051", grpc.WithTransportCredentials(insecure.NewCredentials()))
// 					if err != nil {
// 						return err
// 					}
// 					defer conn.Close()
//
// 					client := proto_auth_v1.NewAuthServiceClient(conn)
// 					req := &proto_auth_v1.RegisterServiceRequest{
// 						ServiceName:    fmt.Sprintf("service-%d", generator.GenerateUserID()),
// 						ServiceVersion: "1.0.0",
// 						Description:    "Benchmark test service",
// 					}
// 					_, err = client.RegisterService(ctx, req)
// 					return err
// 				},
// 			},
// 		},
// 	}
//
// 	runner := benchmark.NewLoadTestRunner(scenario)
// 	runner.RunLoadTest(b)
// }

func addAuthAuthMetadata(ctx context.Context) context.Context {
	md := metadata.New(map[string]string{
		"client-id":  "auth-service-benchmark",
		"client-key": "benchmark-key",
	})
	return metadata.NewOutgoingContext(ctx, md)
}
